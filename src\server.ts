// // This file sets up and starts a Fastify server, registering routes and configuring middleware and environment variables.
// // Import Fastify library for creating the HTTP server
// import Fastify from "fastify";
// // Import environment variables
// import { env } from "./infrastructure/config/env.js";
// // Import Prisma Plugin
// import prismaPlugin from "./infrastructure/db/prismaPlugin.js";
// // Define and export function to build and configure the Fastify server
// export async function buildServer() {
//   // Create a Fastify instance with logger configurations
//   const app = Fastify({
//     logger: {
//       level: process.env.LOG_LEVEL || "info",
//     },
//   });
//   // Register routes and middleware
//   // Register Prisma
//   app.register(prismaPlugin);

//   // Root route
//   app.get('/', async () => {
//     return {
//       message: 'PTEI Backend API',
//       version: '1.0.0',
//       status: 'running',
//       endpoints: {
//         auth: '/api/auth',
//         departments: '/api/departments',
//         roles: '/api/roles',
//         universities: '/api/universities',
//         tslsToUniDropbox: '/api/tsls-to-uni-dropbox',
//         uniToTslsDropbox: '/api/uni-to-tsls-dropbox',
//         uniToTslsReportSubmission: '/api/uni-to-tsls-report-submission'
//       }
//     };
//   });

//   // Health check route
//   app.get('/health', async () => {
//     return { status: 'healthy', timestamp: new Date().toISOString() };
//   });

//   // Register API routes
//   await app.register(import('./adapters/http/routes/auth.js'), { prefix: '/api/auth' });
//   await app.register(import('./adapters/http/routes/departments.js'), { prefix: '/api/departments' });
//   await app.register(import('./adapters/http/routes/roles.js'), { prefix: '/api/roles' });
//   await app.register(import('./adapters/http/routes/universities.js'), { prefix: '/api/universities' });
//   await app.register(import('./adapters/http/routes/tslsToUniDropbox.js'), { prefix: '/api/tsls-to-uni-dropbox' });
//   await app.register(import('./adapters/http/routes/uniToTslsDropbox.js'), { prefix: '/api/uni-to-tsls-dropbox' });
//   await app.register(import('./adapters/http/routes/uniToTslsReportSubmission.js'), { prefix: '/api/uni-to-tsls-report-submission' });

//   return app;
// }
// // Define and export function to start the Fastify server
// export async function startServer() {
//   // Build the Fastify server instance
//   const app = await buildServer();
//   try {
//     // Start the server, listening on the port specified in env.PORT and host '0.0.0.0' for external access
//     await app.listen({ port: Number(env.PORT), host: "0.0.0.0" });
//     // Use the app's logger to log the server start information
//     app.log.info(`Server listening on http://localhost:${env.PORT}`);
//   } catch (error) {
//     app.log.error(error);
//     process.exit(1);
//   }
// }

import Fastify, { FastifyRequest, FastifyReply } from "fastify";
import prismaPlugin from "./infrastructure/db/prismaPlugin.js";
import fastifySwagger from "@fastify/swagger";
import fastifySwaggerUi from "@fastify/swagger-ui";
import { readFileSync } from "fs";
import { fileURLToPath } from "url";
import { dirname, resolve } from "path";

// Get __dirname in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Read version from package.json
const pkg = JSON.parse(
  readFileSync(resolve(__dirname, "../package.json"), "utf-8")
);

export async function buildServer() {
  const app = Fastify({
    logger: {
      level: process.env.LOG_LEVEL || "info",
    },
    // Ensure JSON parsing is enabled with proper limits
    bodyLimit: 50 * 1024 * 1024, // 50MB limit for file uploads
  });

  //OpenAPI 3.0 setup (must use `mode: "dynamic"`)
  app.register(fastifySwagger, {
    mode: "dynamic",
    openapi: {
      openapi: "3.0.0",
      info: {
        title: "PTEI API",
        version: pkg.version,
      },
      servers: [
        {
          url: "http://localhost:3000",
        },
      ],
      
      components: {
        securitySchemes: {
          bearerAuth: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "JWT",
          },
        },
      },
      security: [
        {
          bearerAuth: [],
        },
      ],
    }
  });

  // Swagger UI
  app.register(fastifySwaggerUi, {
    routePrefix: "/swagger-fastify-documentation",
    uiConfig: {
      docExpansion: "none",
      deepLinking: true,
    },
    staticCSP: true,
    transformStaticCSP: (header) => header,
  });

  // Prisma
  app.register(prismaPlugin);

  // Register multipart support for file uploads
  await app.register(import('@fastify/multipart'), {
    limits: {
      fileSize: 50 * 1024 * 1024, // 50MB
      files: 1 // Allow only 1 file per request
    }
  });

  // Root route
  interface RootResponse {
    message: string;
    version: string;
  }

  // Routes
 
  return app;
}

export async function startServer() {
  const app = await buildServer();
  try {
    // Run database seeding if in development mode or if SEED_ON_START is true
    // const shouldSeed = process.env.NODE_ENV === 'development' || process.env.SEED_ON_START === 'true';

    // if (shouldSeed) {
    //   app.log.info('🌱 Running database seeding...');
    //   try {
    //     await initializeSeedData(app.prisma);
    //     app.log.info('✅ Database seeding completed');
    //   } catch (seedError) {
    //     app.log.warn(`⚠️ Database seeding failed: ${String(seedError)}`);
    //     // Don't exit on seeding failure, just warn
    //   }
    // }

    await app.listen({ port: 3000 });
    app.log.info(`✅ Server listening on http://localhost:3000`);
    app.log.info(`📖 Swagger UI: http://localhost:3000/swagger-fastify-documentation`);
    app.log.info(`📌 API Version: ${pkg.version}`);
  } catch (err) {
    app.log.error(err);
    process.exit(1);
  }
}
