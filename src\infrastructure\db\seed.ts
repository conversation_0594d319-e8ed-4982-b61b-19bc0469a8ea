import { PrismaClient } from "../../generated/prisma/index.js";
import { seedData } from "./seedData.js";

const prisma = new PrismaClient();

export async function initializeSeedData(prismaClient?: PrismaClient) {
  const client = prismaClient || prisma;
  
  try {
    console.log("Starting database seeding...");

    // Seed User Types
    console.log("Seeding User Types...");
    for (const userType of seedData.userTypes) {
      await client.userType.upsert({
        where: { UserTypeName: userType.UserTypeName },
        update: {},
        create: userType,
      });
    }

    // Seed User Roles
    console.log("Seeding User Roles...");
    for (const userRole of seedData.userRoles) {
      await client.userRole.upsert({
        where: { UserRoleName: userRole.UserRoleName },
        update: {},
        create: userRole,
      });
    }

    // Seed Departments
    console.log("Seeding Departments...");
    for (const department of seedData.departments) {
      await client.department.upsert({
        where: { DepartmentName: department.DepartmentName },
        update: {},
        create: department,
      });
    }

    // Seed Genders
    console.log("Seeding Genders...");
    for (const gender of seedData.genders) {
      await client.gender.upsert({
        where: { GenderName: gender.GenderName },
        update: {},
        create: gender,
      });
    }

    // Seed Titles
    console.log("Seeding Titles...");
    for (const title of seedData.titles) {
      await client.title.upsert({
        where: { TitleName: title.TitleName },
        update: {},
        create: title,
      });
    }

    // Seed Account Statuses
    console.log("Seeding Account Statuses...");
    for (const accountStatus of seedData.accountStatuses) {
      await client.accountStatus.upsert({
        where: { AccountStatusName: accountStatus.AccountStatusName },
        update: {},
        create: accountStatus,
      });
    }

    // Seed Ethnicities
    console.log("Seeding Ethnicities...");
    for (const ethnicity of seedData.ethnicities) {
      await client.ethnicity.upsert({
        where: { EthnicityName: ethnicity.EthnicityName },
        update: {},
        create: ethnicity,
      });
    }

    // Seed Provinces
    console.log("Seeding Provinces...");
    for (const province of seedData.provinces) {
      await client.province.upsert({
        where: { ProvinceName: province.ProvinceName },
        update: {},
        create: province,
      });
    }

    // Seed Divisions
    console.log(" Seeding Divisions...");
    for (const division of seedData.divisions) {
      await client.division.upsert({
        where: { DivisionName: division.DivisionName },
        update: {},
        create: division,
      });
    }

    // Seed Districts
    console.log(" Seeding Districts...");
    for (const district of seedData.districts) {
      await client.district.upsert({
        where: { DistrictName: district.DistrictName },
        update: {},
        create: district,
      });
    }

    console.log("✅ Database seeding completed successfully!");
    
  } catch (error) {
    console.error("❌ Error during database seeding:", error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeSeedData()
    .then(() => {
      console.log(" ✅ Seeding completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Seeding failed:", error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
