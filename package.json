{"name": "ptei_backend_revamped", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "seed": "tsx src/infrastructure/db/seed.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "npm run seed", "dev:seed": "set SEED_ON_START=true && npm run dev"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@fastify/multipart": "^9.0.3", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@prisma/client": "^6.15.0", "fastify": "^5.5.0", "fastify-plugin": "^5.0.1", "swagger-ui": "^5.27.1"}, "devDependencies": {"@types/node": "^24.3.0", "prisma": "^6.14.0", "tsx": "^4.20.4", "typescript": "^5.9.2"}}