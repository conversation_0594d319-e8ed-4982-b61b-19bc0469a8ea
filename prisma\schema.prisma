// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

model UserType {
  UserTypeId        String   @id @default(uuid()) @db.UniqueIdentifier
  UserTypeName      String  @unique
  UserTypeCreatedAt DateTime @default(now()) @db.DateTime
  UserTypeModifiedAt DateTime @updatedAt @db.DateTime
  UserTypeCreatedBy String
  UserTypeModifiedBy String?

  users             User[]
}

model UserRole {
  UserRoleId        String   @id @default(uuid()) @db.UniqueIdentifier
  UserRoleName      String
  UserRoleCreatedAt DateTime @default(now()) @db.DateTime
  UserRoleModifiedAt DateTime @updatedAt @db.DateTime
  UserRoleCreatedBy String
  UserRoleModifiedBy String?

  departmentToRoleMappings DepartmentToRoleMapping[]
}

model Department {
  DepartmentId      String   @id @default(uuid()) @db.UniqueIdentifier
  DepartmentName    String
  DepartmentCreatedAt DateTime @default(now()) @db.DateTime
  DepartmentModifiedAt DateTime @updatedAt @db.DateTime
  DepartmentCreatedBy String
  DepartmentModifiedBy String?

  departmentToRoleMappings DepartmentToRoleMapping[]
}

model AccountStatus {
  AccountStatusId   String   @id @default(uuid()) @db.UniqueIdentifier
  AccountStatusName String
  AccountStatusCreatedAt DateTime @default(now()) @db.DateTime
  AccountStatusModifiedAt DateTime @updatedAt @db.DateTime
  AccountStatusCreatedBy String
  AccountStatusModifiedBy String?

  users             User[]
}

model Gender {
  GenderId          String   @id @default(uuid()) @db.UniqueIdentifier
  GenderName        String
  GenderCreatedAt   DateTime @default(now()) @db.DateTime
  GenderModifiedAt  DateTime @updatedAt @db.DateTime
  GenderCreatedBy   String
  GenderModifiedBy  String?

  users             User[]
}

model Ethnicity {
  EthnicityId       String   @id @default(uuid()) @db.UniqueIdentifier
  EthnicityName     String
  EthnicityCreatedAt DateTime @default(now()) @db.DateTime
  EthnicityModifiedAt DateTime @updatedAt @db.DateTime
  EthnicityCreatedBy String
  EthnicityModifiedBy String?

  users             User[]
}

model Title {
  TitleId           String   @id @default(uuid()) @db.UniqueIdentifier
  TitleName         String
  TitleCreatedAt    DateTime @default(now()) @db.DateTime
  TitleModifiedAt   DateTime @updatedAt @db.DateTime
  TitleCreatedBy    String
  TitleModifiedBy   String?

  users             User[]
}

model DepartmentToRoleMapping {
  DepartmentToRoleMappingId        String   @id @default(uuid()) @db.UniqueIdentifier
  DepartmentToRoleMappingCreatedAt DateTime @default(now()) @db.DateTime
  DepartmentToRoleMappingModifiedAt DateTime @updatedAt @db.DateTime
  DepartmentToRoleMappingCreatedBy String
  DepartmentToRoleMappingModifiedBy String?
  DepartmentId                     String   @db.UniqueIdentifier
  RoleId                           String   @db.UniqueIdentifier

  department                       Department @relation(fields: [DepartmentId], references: [DepartmentId])
  role                             UserRole   @relation(fields: [RoleId], references: [UserRoleId])
  users                            User[]
}

model Logs {
  LogId             String   @id @default(uuid()) @db.UniqueIdentifier
  LogDescription    String
  LogIPAddress      String
  ActivityTimeStamp DateTime @db.DateTime
  SessionId         String
  DeviceInformation String
  BrowserInformation String
  Geolocation       String?
  ActionType        String
}

model Province {
  ProvinceId        String   @id @default(uuid()) @db.UniqueIdentifier
  ProvinceName      String
  ProvinceCreatedAt DateTime @default(now()) @db.DateTime
  ProvinceModifiedAt DateTime @updatedAt @db.DateTime
  ProvinceCreatedBy String
  ProvinceModifiedBy String?

  students          Student[]
  guarantors        Guarantor[]
}

model Division {
  DivisionId        String    @id @default(uuid()) @db.UniqueIdentifier
  DivisionName      String
  DivisionCreatedAt DateTime  @default(now()) @db.DateTime
  DivisionModifiedAt DateTime @updatedAt @db.DateTime
  DivisionCreatedBy String
  DivisionModifiedBy String?

  students          Student[] @relation("DivisionToStudents")
}

model BankName {
  BankNameId        String   @id @default(uuid()) @db.UniqueIdentifier
  NameOfTheBank     String
  BankNameCreatedAt DateTime @default(now()) @db.DateTime
  BankNameModifiedAt DateTime @updatedAt @db.DateTime
  BankNameCreatedBy String
  BankNameModifiedBy String?

  students          Student[]
}

model NetworkType {
  NetworkTypeId     String   @id @default(uuid()) @db.UniqueIdentifier
  NetworkTypeName   String
  NetworkTypeCreatedAt DateTime @default(now()) @db.DateTime
  NetworkTypeModifiedAt DateTime @updatedAt @db.DateTime
  NetworkTypeCreatedBy String
  NetworkTypeModifiedBy String?

  students          Student[]
}

model AllowanceType {
  AllowanceTypeId   String   @id @default(uuid()) @db.UniqueIdentifier
  AllowanceTypeName String
  AllowanceTypeCreatedAt DateTime @default(now()) @db.DateTime
  AllowanceTypeModifiedAt DateTime @updatedAt @db.DateTime
  AllowanceTypeCreatedBy String
  AllowanceTypeModifiedBy String?

  students          Student[]
}

model ScholarshipStatus {
  ScholarshipStatusId   String   @id @default(uuid()) @db.UniqueIdentifier
  ScholarshipStatusName String
  ScholarshipStatusCreatedAt DateTime @default(now()) @db.DateTime
  ScholarshipStatusModifiedAt DateTime @updatedAt @db.DateTime
  ScholarshipStatusCreatedBy String
  ScholarshipStatusModifiedBy String?

  students              Student[]
}

model District {
  DistrictId        String    @id @default(uuid()) @db.UniqueIdentifier
  DistrictName      String
  DistrictCreatedAt DateTime  @default(now()) @db.DateTime
  DistrictModifiedAt DateTime @updatedAt @db.DateTime
  DistrictCreatedBy String
  DistrictModifiedBy String?

  students          Student[] @relation("DistrictToStudents")
}


model TSLSScheme {
  TSLSSchemeId      String   @id @default(uuid()) @db.UniqueIdentifier
  TSLSSchemeName    String
  TSLSSchemeCreatedAt DateTime @default(now()) @db.DateTime
  TSLSSchemeModifiedAt DateTime @updatedAt @db.DateTime
  TSLSSchemeCreatedBy String
  TSLSSchemeModifiedBy String?

  students          Student[]
}

model Campus {
  CampusId          String   @id @default(uuid()) @db.UniqueIdentifier
  CampusName        String
  CampusCreatedAt   DateTime @default(now()) @db.DateTime
  CampusModifiedAt  DateTime @updatedAt @db.DateTime
  CampusCreatedBy   String
  CampusModifiedBy  String?

  universityToCampusMappings UniversityToCampusMapping[]
}

model UniversityToCampusMapping {
  UniversityToCampusMappingId        String   @id @default(uuid()) @db.UniqueIdentifier
  UniversityToCampusMappingCreatedAt DateTime @default(now()) @db.DateTime
  UniversityToCampusMappingModifiedAt DateTime @updatedAt @db.DateTime
  UniversityToCampusMappingCreatedBy String
  UniversityToCampusMappingModifiedBy String?
  CampusId                           String   @db.UniqueIdentifier
  UniversityId                       String   @db.UniqueIdentifier

  campus                             Campus     @relation(fields: [CampusId], references: [CampusId])
  university                         University @relation(fields: [UniversityId], references: [UniversityId])
  students                           Student[]
}

model SchemeName {
  SchemeNameId      String   @id @default(uuid()) @db.UniqueIdentifier
  SchemeName        String
  SchemeNameCreatedAt DateTime @default(now()) @db.DateTime
  SchemeNameModifiedAt DateTime @updatedAt @db.DateTime
  SchemeNameCreatedBy String
  SchemeNameModifiedBy String?

  programmes        Programme[]
}

model SubSchemeName {
  SubSchemeNameId   String   @id @default(uuid()) @db.UniqueIdentifier
  SubSchemeNameName String
  SubSchemeNameCreatedAt DateTime @default(now()) @db.DateTime
  SubSchemeNameModifiedAt DateTime @updatedAt @db.DateTime
  SubSchemeNameCreatedBy String
  SubSchemeNameModifiedBy String?

  programmes        Programme[]
}

model SubSchemeNew {
  SubSchemeNewId    String   @id @default(uuid()) @db.UniqueIdentifier
  SubSchemeNewName  String
  SubSchemeNewCreatedAt DateTime @default(now()) @db.DateTime
  SubSchemeNewModifiedAt DateTime @updatedAt @db.DateTime
  SubSchemeNewCreatedBy String
  SubSchemeNewModifiedBy String?

  programmes        Programme[]
}

model Programme {
  ProgrammeId       String   @id @default(uuid()) @db.UniqueIdentifier
  ProgrammeName     String
  ProgrammeCreatedAt DateTime @default(now()) @db.DateTime
  ProgrammeModifiedAt DateTime @updatedAt @db.DateTime
  ProgrammeCreatedBy String
  ProgrammeModifiedBy String?
  MajorA            String?
  MajorB            String?
  Minor             String?
  Discipline        String
  DurationOfAwards  String
  SchemeNameId      String   @db.UniqueIdentifier
  SubSchemeNameId   String   @db.UniqueIdentifier
  SubSchemeNewId    String   @db.UniqueIdentifier

  schemeName        SchemeName    @relation(fields: [SchemeNameId], references: [SchemeNameId])
  subSchemeName     SubSchemeName @relation(fields: [SubSchemeNameId], references: [SubSchemeNameId])
  subSchemeNew      SubSchemeNew  @relation(fields: [SubSchemeNewId], references: [SubSchemeNewId])
  students          Student[]
}

model MyCash {
  MyCashId          String   @id @default(uuid()) @db.UniqueIdentifier
  MyCashName        String
  DateTimeCreated   DateTime @default(now()) @db.DateTime
  DateTimeModified  DateTime @updatedAt @db.DateTime
  UserCreatedBy     String
  UserModifiedBy    String?

  students          Student[]
}

model User {
  UserId                    String   @id @default(uuid()) @db.UniqueIdentifier
  TIN                       String
  FirstName                 String
  LastName                  String
  OtherName                 String?
  Email                     String
  HashedPassword            String
  UserProfilePicPath        String?
  IsAccountLoggedOut        Boolean
  FailedLoginAttempts       Int?
  IsUserLoggedIn            Boolean
  UserCreatedAt             DateTime @default(now()) @db.DateTime
  UserModifiedAt            DateTime @updatedAt @db.DateTime
  EmailVerificationToken    String?
  EmailVerificationTokenExpiry DateTime?
  PasswordResetToken        String?
  PasswordResetTokenExpiry  DateTime?
  LastLogin                 DateTime?
  LastPasswordResetDate     DateTime?
  PhoneNumber               Int?
  DepartmentToRoleMappingId String   @db.UniqueIdentifier
  UserTypeId                String   @db.UniqueIdentifier
  AccountStatusId           String   @db.UniqueIdentifier
  TitleId                   String   @db.UniqueIdentifier
  GenderId                  String   @db.UniqueIdentifier
  EthnicityId               String   @db.UniqueIdentifier

  departmentToRoleMapping   DepartmentToRoleMapping @relation(fields: [DepartmentToRoleMappingId], references: [DepartmentToRoleMappingId])
  userType                  UserType @relation(fields: [UserTypeId], references: [UserTypeId])
  accountStatus             AccountStatus @relation(fields: [AccountStatusId], references: [AccountStatusId])
  title                     Title @relation(fields: [TitleId], references: [TitleId])
  gender                    Gender @relation(fields: [GenderId], references: [GenderId])
  ethnicity                 Ethnicity @relation(fields: [EthnicityId], references: [EthnicityId])
  staff                     Staff[]
  students                  Student[]
  guarantors                Guarantor[]
  organizations             Organization[]
  universities              University[]
}

model Staff {
  StaffId           String   @id @default(uuid()) @db.UniqueIdentifier
  StaffCreatedAt    DateTime @default(now()) @db.DateTime
  StaffModifiedAt   DateTime @updatedAt @db.DateTime
  StaffCreatedBy    String
  StaffModifiedBy   String?
  userId            String   @db.UniqueIdentifier

  user              User     @relation(fields: [userId], references: [UserId])
}

model Student {
  StudentId                 String   @id @default(uuid()) @db.UniqueIdentifier
  StudentCreatedAt          DateTime @default(now()) @db.DateTime
  StudentModifiedAt         DateTime @updatedAt @db.DateTime
  StudentCreatedBy          String
  StudentModifiedBy         String?
  StudentIDNumber           String
  DateOfBirth               DateTime? @db.DateTime
  ResidentialAddress        String
  PostalAddress             String?
  DateOfOffer               DateTime @db.DateTime
  CommencementTerm          String
  CommencementYear          String
  EndTerm                   String
  EndYear                   String
  Rent                      String?
  Food                      String?
  BusFare                   String?
  IncidentalAllowance      String?
  BirthRegistrationNumber   Int
  PassportNumber            String?
  Hostel                    String?
  BankAccountNumber         String?
  ETicketCardNumber         String?
  ApplicationId             String
  Remarks                   String
  UniversityToCampusMappingId String? @db.UniqueIdentifier
  BankNameId                String   @db.UniqueIdentifier
  ProgrammeId               String   @db.UniqueIdentifier
  MyCashId                  String   @db.UniqueIdentifier
  NetworkTypeId             String   @db.UniqueIdentifier
  ProvinceId                String   @db.UniqueIdentifier
  ScholarshipStatusId       String   @db.UniqueIdentifier
  TSLSSchemeId              String   @db.UniqueIdentifier
  AllowanceTypeId           String   @db.UniqueIdentifier
  userId                    String   @db.UniqueIdentifier
  DivisionId                String?  @db.UniqueIdentifier
  DistrictId                String?  @db.UniqueIdentifier

  universityToCampusMapping UniversityToCampusMapping? @relation(fields: [UniversityToCampusMappingId], references: [UniversityToCampusMappingId], onDelete: NoAction, onUpdate: NoAction)
  bankName                  BankName @relation(fields: [BankNameId], references: [BankNameId])
  programme                 Programme @relation(fields: [ProgrammeId], references: [ProgrammeId])
  myCash                    MyCash @relation(fields: [MyCashId], references: [MyCashId])
  networkType               NetworkType @relation(fields: [NetworkTypeId], references: [NetworkTypeId])
  province                  Province @relation(fields: [ProvinceId], references: [ProvinceId])
  scholarshipStatus         ScholarshipStatus @relation(fields: [ScholarshipStatusId], references: [ScholarshipStatusId])
  tslsScheme                TSLSScheme @relation(fields: [TSLSSchemeId], references: [TSLSSchemeId])
  allowanceType             AllowanceType @relation(fields: [AllowanceTypeId], references: [AllowanceTypeId])
  user                      User @relation(fields: [userId], references: [UserId])
}

model Guarantor {
  GuarantorId               String   @id @default(uuid()) @db.UniqueIdentifier
  GuarantorCreatedAt        DateTime @default(now()) @db.DateTime
  GuarantorModifiedAt       DateTime @updatedAt @db.DateTime
  GuarantorCreatedBy        String
  GuarantorModifiedBy       String?
  DateOfBirth               DateTime @db.DateTime
  ResidentialAddress        String
  PostalAddress             String?
  BirthRegistrationNumber   Int
  PassportNumber            String?
  ProvinceId                String   @db.UniqueIdentifier
  userId                    String   @db.UniqueIdentifier

  province                  Province @relation(fields: [ProvinceId], references: [ProvinceId])
  user                      User @relation(fields: [userId], references: [UserId])
}

model Organization {
  OrganizationId                    String   @id @default(uuid()) @db.UniqueIdentifier
  OrganizationName                  String
  OrganizationBranch                String
  OrganizationRegistrationNumber    String
  OrganizationRepresentativeFirstName String
  OrganizationRepresentativeLastName String
  OrganizationRepresentativeOtherName String?
  Position                          String
  OrganizationCreatedAt             DateTime @default(now()) @db.DateTime
  OrganizationModifiedAt            DateTime @updatedAt @db.DateTime
  OrganizationCreatedBy             String
  OrganizationModifiedBy            String?
  userId                            String   @db.UniqueIdentifier

  user                              User @relation(fields: [userId], references: [UserId])
}

model University {
  UniversityId                      String   @id @default(uuid()) @db.UniqueIdentifier
  UniversityName                    String
  PrimaryEmail                      String
  CCEmail                           String?
  BCCEmail                          String?
  UniversityRepresentativeFirstName String
  UniversityRepresentativeLastName  String
  UniversityRepresentativeOtherName String?
  UniversityCreatedAt               DateTime @default(now()) @db.DateTime
  UniversityModifiedAt              DateTime @updatedAt @db.DateTime
  UniversityCreatedBy               String
  UniversityModifiedBy              String?
  userId                            String   @db.UniqueIdentifier

  user                              User @relation(fields: [userId], references: [UserId])
  universityToCampusMappings        UniversityToCampusMapping[]
}