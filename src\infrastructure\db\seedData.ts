// Seed data for lookup tables
export const seedData = {
  userTypes: [
    { UserTypeName: "Student", UserTypeCreatedBy: "SYSTEM" },
    { UserTypeName: "Organization", UserTypeCreatedBy: "SYSTEM" },
    { UserTypeName: "Guarantor", UserTypeCreatedBy: "SYSTEM" },
    { UserTypeName: "Staff", UserTypeCreatedBy: "SYSTEM" },
    { UserTypeName: "University", UserTypeCreatedBy: "SYSTEM" }
  ],

  userRoles: [
    { UserRoleName: "Super Admin", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Admin", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Officer", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Tier One", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Tier Two", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Intern", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Student", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Organization Officer", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "University Officer", UserRoleCreatedBy: "SYSTEM" }
  ],

  departments: [
    { DepartmentName: "IT", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Finance", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Student Services", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Customer Services Officer", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Learning Support", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "COMMS", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "HR", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Audit", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Recoveries", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Student", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "University", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Organization", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Guarantor", DepartmentCreatedBy: "SYSTEM" }
  ],

  genders: [
    { GenderName: "Male", GenderCreatedBy: "SYSTEM" },
    { GenderName: "Female", GenderCreatedBy: "SYSTEM" },
    { GenderName: "Other", GenderCreatedBy: "SYSTEM" }
  ],

  titles: [
    { TitleName: "Mr", TitleCreatedBy: "SYSTEM" },
    { TitleName: "Mrs", TitleCreatedBy: "SYSTEM" },
    { TitleName: "Miss", TitleCreatedBy: "SYSTEM" },
    { TitleName: "Ms", TitleCreatedBy: "SYSTEM" }
  ],

  accountStatuses: [
    { AccountStatusName: "Active", AccountStatusCreatedBy: "SYSTEM" },
    { AccountStatusName: "Inactive", AccountStatusCreatedBy: "SYSTEM" },
    { AccountStatusName: "Suspended", AccountStatusCreatedBy: "SYSTEM" },
    { AccountStatusName: "Pending", AccountStatusCreatedBy: "SYSTEM" }
  ],

  ethnicities: [
    { EthnicityName: "iTaukei", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Indo-Fijian", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Rotuman", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Chinese", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "European", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Other Pacific Islander", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Mixed", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Other", EthnicityCreatedBy: "SYSTEM" }
  ]
};
