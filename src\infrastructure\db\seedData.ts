// Seed data for lookup tables
export const seedData = {
  userTypes: [
    { UserTypeName: "Student", UserTypeCreatedBy: "SYSTEM" },
    { UserTypeName: "Organization", UserTypeCreatedBy: "SYSTEM" },
    { UserTypeName: "Guarantor", UserTypeCreatedBy: "SYSTEM" },
    { UserTypeName: "Staff", UserTypeCreatedBy: "SYSTEM" },
    { UserTypeName: "University", UserTypeCreatedBy: "SYSTEM" }
  ],

  userRoles: [
    { UserRoleName: "Super Admin", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Admin", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Officer", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Tier One", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Tier Two", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Intern", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Student", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "Organization Officer", UserRoleCreatedBy: "SYSTEM" },
    { UserRoleName: "University Officer", UserRoleCreatedBy: "SYSTEM" }
  ],

  departments: [
    { DepartmentName: "IT", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Finance", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Student Services", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Customer Services Officer", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Learning Support", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "COMMS", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "HR", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Audit", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Recoveries", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Student", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "University", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Organization", DepartmentCreatedBy: "SYSTEM" },
    { DepartmentName: "Guarantor", DepartmentCreatedBy: "SYSTEM" }
  ],

  genders: [
    { GenderName: "Male", GenderCreatedBy: "SYSTEM" },
    { GenderName: "Female", GenderCreatedBy: "SYSTEM" },
    { GenderName: "Other", GenderCreatedBy: "SYSTEM" }
  ],

  titles: [
    { TitleName: "Mr", TitleCreatedBy: "SYSTEM" },
    { TitleName: "Mrs", TitleCreatedBy: "SYSTEM" },
    { TitleName: "Miss", TitleCreatedBy: "SYSTEM" },
    { TitleName: "Ms", TitleCreatedBy: "SYSTEM" }
  ],

  accountStatuses: [
    { AccountStatusName: "Active", AccountStatusCreatedBy: "SYSTEM" },
    { AccountStatusName: "Inactive", AccountStatusCreatedBy: "SYSTEM" },
    { AccountStatusName: "Suspended", AccountStatusCreatedBy: "SYSTEM" },
    { AccountStatusName: "Pending", AccountStatusCreatedBy: "SYSTEM" }
  ],

  ethnicities: [
    { EthnicityName: "iTaukei", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Indo-Fijian", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Rotuman", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Chinese", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "European", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Other Pacific Islander", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Mixed", EthnicityCreatedBy: "SYSTEM" },
    { EthnicityName: "Other", EthnicityCreatedBy: "SYSTEM" }
  ],

  provinces: [
    { ProvinceName: "Ba", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Bua", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Cakaudrove", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Kadavu", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Lau", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Lomaiviti", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Macuata", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Nadroga-Navosa", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Naitasiri", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Namosi", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Ra", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Rewa", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Serua", ProvinceCreatedBy: "SYSTEM" },
    { ProvinceName: "Tailevu", ProvinceCreatedBy: "SYSTEM" }
  ],

  divisions: [
    { DivisionName: "Central", DivisionCreatedBy: "SYSTEM" },
    { DivisionName: "Western", DivisionCreatedBy: "SYSTEM" },
    { DivisionName: "Northern", DivisionCreatedBy: "SYSTEM" },
    { DivisionName: "Eastern", DivisionCreatedBy: "SYSTEM" }
  ],

  districts: [
    { DistrictName: "Suva", DistrictCreatedBy: "SYSTEM" },
    { DistrictName: "Nasinu", DistrictCreatedBy: "SYSTEM" },
    { DistrictName: "Lautoka", DistrictCreatedBy: "SYSTEM" },
    { DistrictName: "Nadi", DistrictCreatedBy: "SYSTEM" },
    { DistrictName: "Labasa", DistrictCreatedBy: "SYSTEM" },
    { DistrictName: "Ba", DistrictCreatedBy: "SYSTEM" },
    { DistrictName: "Tavua", DistrictCreatedBy: "SYSTEM" },
    { DistrictName: "Rakiraki", DistrictCreatedBy: "SYSTEM" },
    { DistrictName: "Sigatoka", DistrictCreatedBy: "SYSTEM" },
    { DistrictName: "Navua", DistrictCreatedBy: "SYSTEM" }
  ]
};
