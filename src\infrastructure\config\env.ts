// This file is responsible for loading environment variables from a .env file and making them available throughout the application.
// Import the dotenv package to load environment variables from a .env file.
import dotenv from "dotenv";
// Load environment variables from .env file
dotenv.config();
// Export an object containing environment variables with fallback values
export const env = {
  // Server port number
  PORT: process.env.PORT || 4000,
  // Node environmnt (development || production)
  NODE_ENV: process.env.NODE_ENV || "development",
  // Database connection string
  DATABASE_URL: process.env.DATABASE_URL || "localhost",
  // Database Username
  //DATABASE_USERNAME: process.env.DB_USERNAME || "root",
  // Database Password
  //DATABASE_PASSWORD: process.env.DB_PASSWORD || "password",
  // Secret Key for JWT authentication
  JWT_SECRET: process.env.JWT_SECRET || "Your_Very_Strong_Super_Secret_Key",
  // Log Level
  LOG_LEVEL: process.env.LOG_LEVEL || "info",
  // SuperAdmin configuration
  SUPER_ADMIN_EMAIL: process.env.SUPER_ADMIN_EMAIL || "<EMAIL>",
  SUPER_ADMIN_PASSWORD: process.env.SUPER_ADMIN_PASSWORD || "Testing@2025",
};