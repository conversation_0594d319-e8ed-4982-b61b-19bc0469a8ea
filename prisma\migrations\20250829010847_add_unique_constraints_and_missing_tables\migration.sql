/*
  Warnings:

  - A unique constraint covering the columns `[AccountStatusName]` on the table `AccountStatus` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[DepartmentName]` on the table `Department` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[EthnicityName]` on the table `Ethnicity` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[GenderName]` on the table `Gender` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[ProvinceName]` on the table `Province` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[TitleName]` on the table `Title` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[UserRoleName]` on the table `UserRole` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[UserTypeName]` on the table `UserType` will be added. If there are existing duplicate values, this will fail.

*/
BEGIN TRY

BEGIN TRAN;

-- AlterTable
ALTER TABLE [dbo].[Student] ADD [DistrictId] UNIQUEIDENTIFIER,
[DivisionId] UNIQUEIDENTIFIER;

-- CreateTable
CREATE TABLE [dbo].[Division] (
    [DivisionId] UNIQUEIDENTIFIER NOT NULL,
    [DivisionName] NVARCHAR(1000) NOT NULL,
    [DivisionCreatedAt] DATETIME NOT NULL CONSTRAINT [Division_DivisionCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [DivisionModifiedAt] DATETIME NOT NULL,
    [DivisionCreatedBy] NVARCHAR(1000) NOT NULL,
    [DivisionModifiedBy] NVARCHAR(1000),
    CONSTRAINT [Division_pkey] PRIMARY KEY CLUSTERED ([DivisionId]),
    CONSTRAINT [Division_DivisionName_key] UNIQUE NONCLUSTERED ([DivisionName])
);

-- CreateTable
CREATE TABLE [dbo].[District] (
    [DistrictId] UNIQUEIDENTIFIER NOT NULL,
    [DistrictName] NVARCHAR(1000) NOT NULL,
    [DistrictCreatedAt] DATETIME NOT NULL CONSTRAINT [District_DistrictCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [DistrictModifiedAt] DATETIME NOT NULL,
    [DistrictCreatedBy] NVARCHAR(1000) NOT NULL,
    [DistrictModifiedBy] NVARCHAR(1000),
    CONSTRAINT [District_pkey] PRIMARY KEY CLUSTERED ([DistrictId]),
    CONSTRAINT [District_DistrictName_key] UNIQUE NONCLUSTERED ([DistrictName])
);

-- CreateIndex
ALTER TABLE [dbo].[AccountStatus] ADD CONSTRAINT [AccountStatus_AccountStatusName_key] UNIQUE NONCLUSTERED ([AccountStatusName]);

-- CreateIndex
ALTER TABLE [dbo].[Department] ADD CONSTRAINT [Department_DepartmentName_key] UNIQUE NONCLUSTERED ([DepartmentName]);

-- CreateIndex
ALTER TABLE [dbo].[Ethnicity] ADD CONSTRAINT [Ethnicity_EthnicityName_key] UNIQUE NONCLUSTERED ([EthnicityName]);

-- CreateIndex
ALTER TABLE [dbo].[Gender] ADD CONSTRAINT [Gender_GenderName_key] UNIQUE NONCLUSTERED ([GenderName]);

-- CreateIndex
ALTER TABLE [dbo].[Province] ADD CONSTRAINT [Province_ProvinceName_key] UNIQUE NONCLUSTERED ([ProvinceName]);

-- CreateIndex
ALTER TABLE [dbo].[Title] ADD CONSTRAINT [Title_TitleName_key] UNIQUE NONCLUSTERED ([TitleName]);

-- CreateIndex
ALTER TABLE [dbo].[UserRole] ADD CONSTRAINT [UserRole_UserRoleName_key] UNIQUE NONCLUSTERED ([UserRoleName]);

-- CreateIndex
ALTER TABLE [dbo].[UserType] ADD CONSTRAINT [UserType_UserTypeName_key] UNIQUE NONCLUSTERED ([UserTypeName]);

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_DivisionId_fkey] FOREIGN KEY ([DivisionId]) REFERENCES [dbo].[Division]([DivisionId]) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_DistrictId_fkey] FOREIGN KEY ([DistrictId]) REFERENCES [dbo].[District]([DistrictId]) ON DELETE SET NULL ON UPDATE CASCADE;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
