import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";

// Define interfaces for request bodies
interface CreateLookupRequest {
  name: string;
  createdBy: string;
}

interface UpdateLookupRequest {
  name?: string;
  modifiedBy: string;
}

interface LookupParams {
  id: string;
}

export default async function lookupsRoutes(fastify: FastifyInstance) {
  // User Types endpoints
  fastify.get('/user-types', {
    schema: {
      tags: ['Lookups'],
      summary: 'Get all user types',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  UserTypeId: { type: 'string' },
                  UserTypeName: { type: 'string' },
                  UserTypeCreatedAt: { type: 'string' },
                  UserTypeModifiedAt: { type: 'string' },
                  UserTypeCreatedBy: { type: 'string' },
                  UserTypeModifiedBy: { type: 'string', nullable: true }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userTypes = await fastify.prisma.userType.findMany({
        orderBy: { UserTypeName: 'asc' }
      });
      return reply.send({ success: true, data: userTypes });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to fetch user types' });
    }
  });

  fastify.post('/user-types', {
    schema: {
      tags: ['Lookups'],
      summary: 'Create a new user type',
      body: {
        type: 'object',
        required: ['name', 'createdBy'],
        properties: {
          name: { type: 'string' },
          createdBy: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: CreateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { name, createdBy } = request.body;
      const userType = await fastify.prisma.userType.create({
        data: {
          UserTypeName: name,
          UserTypeCreatedBy: createdBy
        }
      });
      return reply.status(201).send({ success: true, data: userType });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to create user type' });
    }
  });

  fastify.put('/user-types/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Update a user type',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        required: ['modifiedBy'],
        properties: {
          name: { type: 'string' },
          modifiedBy: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: LookupParams; Body: UpdateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { name, modifiedBy } = request.body;
      
      const updateData: any = { UserTypeModifiedBy: modifiedBy };
      if (name) updateData.UserTypeName = name;

      const userType = await fastify.prisma.userType.update({
        where: { UserTypeId: id },
        data: updateData
      });
      return reply.send({ success: true, data: userType });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to update user type' });
    }
  });

  fastify.delete('/user-types/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Delete a user type',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: LookupParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      await fastify.prisma.userType.delete({
        where: { UserTypeId: id }
      });
      return reply.send({ success: true, message: 'User type deleted successfully' });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to delete user type' });
    }
  });

  // User Roles endpoints
  fastify.get('/user-roles', {
    schema: {
      tags: ['Lookups'],
      summary: 'Get all user roles'
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userRoles = await fastify.prisma.userRole.findMany({
        orderBy: { UserRoleName: 'asc' }
      });
      return reply.send({ success: true, data: userRoles });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to fetch user roles' });
    }
  });

  fastify.post('/user-roles', {
    schema: {
      tags: ['Lookups'],
      summary: 'Create a new user role'
    }
  }, async (request: FastifyRequest<{ Body: CreateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { name, createdBy } = request.body;
      const userRole = await fastify.prisma.userRole.create({
        data: {
          UserRoleName: name,
          UserRoleCreatedBy: createdBy
        }
      });
      return reply.status(201).send({ success: true, data: userRole });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to create user role' });
    }
  });

  fastify.put('/user-roles/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Update a user role'
    }
  }, async (request: FastifyRequest<{ Params: LookupParams; Body: UpdateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { name, modifiedBy } = request.body;
      
      const updateData: any = { UserRoleModifiedBy: modifiedBy };
      if (name) updateData.UserRoleName = name;

      const userRole = await fastify.prisma.userRole.update({
        where: { UserRoleId: id },
        data: updateData
      });
      return reply.send({ success: true, data: userRole });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to update user role' });
    }
  });

  fastify.delete('/user-roles/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Delete a user role'
    }
  }, async (request: FastifyRequest<{ Params: LookupParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      await fastify.prisma.userRole.delete({
        where: { UserRoleId: id }
      });
      return reply.send({ success: true, message: 'User role deleted successfully' });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to delete user role' });
    }
  });

  // Departments endpoints
  fastify.get('/departments', {
    schema: {
      tags: ['Lookups'],
      summary: 'Get all departments'
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const departments = await fastify.prisma.department.findMany({
        orderBy: { DepartmentName: 'asc' }
      });
      return reply.send({ success: true, data: departments });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to fetch departments' });
    }
  });

  fastify.post('/departments', {
    schema: {
      tags: ['Lookups'],
      summary: 'Create a new department'
    }
  }, async (request: FastifyRequest<{ Body: CreateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { name, createdBy } = request.body;
      const department = await fastify.prisma.department.create({
        data: {
          DepartmentName: name,
          DepartmentCreatedBy: createdBy
        }
      });
      return reply.status(201).send({ success: true, data: department });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to create department' });
    }
  });

  fastify.put('/departments/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Update a department'
    }
  }, async (request: FastifyRequest<{ Params: LookupParams; Body: UpdateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { name, modifiedBy } = request.body;
      
      const updateData: any = { DepartmentModifiedBy: modifiedBy };
      if (name) updateData.DepartmentName = name;

      const department = await fastify.prisma.department.update({
        where: { DepartmentId: id },
        data: updateData
      });
      return reply.send({ success: true, data: department });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to update department' });
    }
  });

  fastify.delete('/departments/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Delete a department'
    }
  }, async (request: FastifyRequest<{ Params: LookupParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      await fastify.prisma.department.delete({
        where: { DepartmentId: id }
      });
      return reply.send({ success: true, message: 'Department deleted successfully' });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to delete department' });
    }
  });

  // Genders endpoints
  fastify.get('/genders', {
    schema: {
      tags: ['Lookups'],
      summary: 'Get all genders'
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const genders = await fastify.prisma.gender.findMany({
        orderBy: { GenderName: 'asc' }
      });
      return reply.send({ success: true, data: genders });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to fetch genders' });
    }
  });

  fastify.post('/genders', {
    schema: {
      tags: ['Lookups'],
      summary: 'Create a new gender'
    }
  }, async (request: FastifyRequest<{ Body: CreateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { name, createdBy } = request.body;
      const gender = await fastify.prisma.gender.create({
        data: {
          GenderName: name,
          GenderCreatedBy: createdBy
        }
      });
      return reply.status(201).send({ success: true, data: gender });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to create gender' });
    }
  });

  fastify.put('/genders/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Update a gender'
    }
  }, async (request: FastifyRequest<{ Params: LookupParams; Body: UpdateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { name, modifiedBy } = request.body;

      const updateData: any = { GenderModifiedBy: modifiedBy };
      if (name) updateData.GenderName = name;

      const gender = await fastify.prisma.gender.update({
        where: { GenderId: id },
        data: updateData
      });
      return reply.send({ success: true, data: gender });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to update gender' });
    }
  });

  fastify.delete('/genders/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Delete a gender'
    }
  }, async (request: FastifyRequest<{ Params: LookupParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      await fastify.prisma.gender.delete({
        where: { GenderId: id }
      });
      return reply.send({ success: true, message: 'Gender deleted successfully' });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to delete gender' });
    }
  });

  // Titles endpoints
  fastify.get('/titles', {
    schema: {
      tags: ['Lookups'],
      summary: 'Get all titles'
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const titles = await fastify.prisma.title.findMany({
        orderBy: { TitleName: 'asc' }
      });
      return reply.send({ success: true, data: titles });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to fetch titles' });
    }
  });

  fastify.post('/titles', {
    schema: {
      tags: ['Lookups'],
      summary: 'Create a new title'
    }
  }, async (request: FastifyRequest<{ Body: CreateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { name, createdBy } = request.body;
      const title = await fastify.prisma.title.create({
        data: {
          TitleName: name,
          TitleCreatedBy: createdBy
        }
      });
      return reply.status(201).send({ success: true, data: title });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to create title' });
    }
  });

  fastify.put('/titles/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Update a title'
    }
  }, async (request: FastifyRequest<{ Params: LookupParams; Body: UpdateLookupRequest }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { name, modifiedBy } = request.body;

      const updateData: any = { TitleModifiedBy: modifiedBy };
      if (name) updateData.TitleName = name;

      const title = await fastify.prisma.title.update({
        where: { TitleId: id },
        data: updateData
      });
      return reply.send({ success: true, data: title });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to update title' });
    }
  });

  fastify.delete('/titles/:id', {
    schema: {
      tags: ['Lookups'],
      summary: 'Delete a title'
    }
  }, async (request: FastifyRequest<{ Params: LookupParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      await fastify.prisma.title.delete({
        where: { TitleId: id }
      });
      return reply.send({ success: true, message: 'Title deleted successfully' });
    } catch (error) {
      return reply.status(500).send({ success: false, error: 'Failed to delete title' });
    }
  });
}
