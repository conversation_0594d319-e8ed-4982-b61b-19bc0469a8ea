BEGIN TRY

BEGIN TRAN;

-- CreateTable
CREATE TABLE [dbo].[UserType] (
    [UserTypeId] UNIQUEIDENTIFIER NOT NULL,
    [UserTypeName] NVARCHAR(1000) NOT NULL,
    [UserTypeCreatedAt] DATETIME NOT NULL CONSTRAINT [UserType_UserTypeCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [UserTypeModifiedAt] DATETIME NOT NULL,
    [UserTypeCreatedBy] NVARCHAR(1000) NOT NULL,
    [UserTypeModifiedBy] NVARCHAR(1000),
    CONSTRAINT [UserType_pkey] PRIMARY KEY CLUSTERED ([UserTypeId])
);

-- CreateTable
CREATE TABLE [dbo].[UserRole] (
    [UserRoleId] UNIQUEIDENTIFIER NOT NULL,
    [UserRoleName] NVARCHAR(1000) NOT NULL,
    [UserRoleCreatedAt] DATETIME NOT NULL CONSTRAINT [UserRole_UserRoleCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [UserRoleModifiedAt] DATETIME NOT NULL,
    [UserRoleCreatedBy] NVARCHAR(1000) NOT NULL,
    [UserRoleModifiedBy] NVARCHAR(1000),
    CONSTRAINT [UserRole_pkey] PRIMARY KEY CLUSTERED ([UserRoleId])
);

-- CreateTable
CREATE TABLE [dbo].[Department] (
    [DepartmentId] UNIQUEIDENTIFIER NOT NULL,
    [DepartmentName] NVARCHAR(1000) NOT NULL,
    [DepartmentCreatedAt] DATETIME NOT NULL CONSTRAINT [Department_DepartmentCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [DepartmentModifiedAt] DATETIME NOT NULL,
    [DepartmentCreatedBy] NVARCHAR(1000) NOT NULL,
    [DepartmentModifiedBy] NVARCHAR(1000),
    CONSTRAINT [Department_pkey] PRIMARY KEY CLUSTERED ([DepartmentId])
);

-- CreateTable
CREATE TABLE [dbo].[AccountStatus] (
    [AccountStatusId] UNIQUEIDENTIFIER NOT NULL,
    [AccountStatusName] NVARCHAR(1000) NOT NULL,
    [AccountStatusCreatedAt] DATETIME NOT NULL CONSTRAINT [AccountStatus_AccountStatusCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [AccountStatusModifiedAt] DATETIME NOT NULL,
    [AccountStatusCreatedBy] NVARCHAR(1000) NOT NULL,
    [AccountStatusModifiedBy] NVARCHAR(1000),
    CONSTRAINT [AccountStatus_pkey] PRIMARY KEY CLUSTERED ([AccountStatusId])
);

-- CreateTable
CREATE TABLE [dbo].[Gender] (
    [GenderId] UNIQUEIDENTIFIER NOT NULL,
    [GenderName] NVARCHAR(1000) NOT NULL,
    [GenderCreatedAt] DATETIME NOT NULL CONSTRAINT [Gender_GenderCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [GenderModifiedAt] DATETIME NOT NULL,
    [GenderCreatedBy] NVARCHAR(1000) NOT NULL,
    [GenderModifiedBy] NVARCHAR(1000),
    CONSTRAINT [Gender_pkey] PRIMARY KEY CLUSTERED ([GenderId])
);

-- CreateTable
CREATE TABLE [dbo].[Ethnicity] (
    [EthnicityId] UNIQUEIDENTIFIER NOT NULL,
    [EthnicityName] NVARCHAR(1000) NOT NULL,
    [EthnicityCreatedAt] DATETIME NOT NULL CONSTRAINT [Ethnicity_EthnicityCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [EthnicityModifiedAt] DATETIME NOT NULL,
    [EthnicityCreatedBy] NVARCHAR(1000) NOT NULL,
    [EthnicityModifiedBy] NVARCHAR(1000),
    CONSTRAINT [Ethnicity_pkey] PRIMARY KEY CLUSTERED ([EthnicityId])
);

-- CreateTable
CREATE TABLE [dbo].[Title] (
    [TitleId] UNIQUEIDENTIFIER NOT NULL,
    [TitleName] NVARCHAR(1000) NOT NULL,
    [TitleCreatedAt] DATETIME NOT NULL CONSTRAINT [Title_TitleCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [TitleModifiedAt] DATETIME NOT NULL,
    [TitleCreatedBy] NVARCHAR(1000) NOT NULL,
    [TitleModifiedBy] NVARCHAR(1000),
    CONSTRAINT [Title_pkey] PRIMARY KEY CLUSTERED ([TitleId])
);

-- CreateTable
CREATE TABLE [dbo].[DepartmentToRoleMapping] (
    [DepartmentToRoleMappingId] UNIQUEIDENTIFIER NOT NULL,
    [DepartmentToRoleMappingCreatedAt] DATETIME NOT NULL CONSTRAINT [DepartmentToRoleMapping_DepartmentToRoleMappingCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [DepartmentToRoleMappingModifiedAt] DATETIME NOT NULL,
    [DepartmentToRoleMappingCreatedBy] NVARCHAR(1000) NOT NULL,
    [DepartmentToRoleMappingModifiedBy] NVARCHAR(1000),
    [DepartmentId] UNIQUEIDENTIFIER NOT NULL,
    [RoleId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [DepartmentToRoleMapping_pkey] PRIMARY KEY CLUSTERED ([DepartmentToRoleMappingId])
);

-- CreateTable
CREATE TABLE [dbo].[Logs] (
    [LogId] UNIQUEIDENTIFIER NOT NULL,
    [LogDescription] NVARCHAR(1000) NOT NULL,
    [LogIPAddress] NVARCHAR(1000) NOT NULL,
    [ActivityTimeStamp] DATETIME NOT NULL,
    [SessionId] NVARCHAR(1000) NOT NULL,
    [DeviceInformation] NVARCHAR(1000) NOT NULL,
    [BrowserInformation] NVARCHAR(1000) NOT NULL,
    [Geolocation] NVARCHAR(1000),
    [ActionType] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [Logs_pkey] PRIMARY KEY CLUSTERED ([LogId])
);

-- CreateTable
CREATE TABLE [dbo].[Province] (
    [ProvinceId] UNIQUEIDENTIFIER NOT NULL,
    [ProvinceName] NVARCHAR(1000) NOT NULL,
    [ProvinceCreatedAt] DATETIME NOT NULL CONSTRAINT [Province_ProvinceCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [ProvinceModifiedAt] DATETIME NOT NULL,
    [ProvinceCreatedBy] NVARCHAR(1000) NOT NULL,
    [ProvinceModifiedBy] NVARCHAR(1000),
    CONSTRAINT [Province_pkey] PRIMARY KEY CLUSTERED ([ProvinceId])
);

-- CreateTable
CREATE TABLE [dbo].[BankName] (
    [BankNameId] UNIQUEIDENTIFIER NOT NULL,
    [NameOfTheBank] NVARCHAR(1000) NOT NULL,
    [BankNameCreatedAt] DATETIME NOT NULL CONSTRAINT [BankName_BankNameCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [BankNameModifiedAt] DATETIME NOT NULL,
    [BankNameCreatedBy] NVARCHAR(1000) NOT NULL,
    [BankNameModifiedBy] NVARCHAR(1000),
    CONSTRAINT [BankName_pkey] PRIMARY KEY CLUSTERED ([BankNameId])
);

-- CreateTable
CREATE TABLE [dbo].[NetworkType] (
    [NetworkTypeId] UNIQUEIDENTIFIER NOT NULL,
    [NetworkTypeName] NVARCHAR(1000) NOT NULL,
    [NetworkTypeCreatedAt] DATETIME NOT NULL CONSTRAINT [NetworkType_NetworkTypeCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [NetworkTypeModifiedAt] DATETIME NOT NULL,
    [NetworkTypeCreatedBy] NVARCHAR(1000) NOT NULL,
    [NetworkTypeModifiedBy] NVARCHAR(1000),
    CONSTRAINT [NetworkType_pkey] PRIMARY KEY CLUSTERED ([NetworkTypeId])
);

-- CreateTable
CREATE TABLE [dbo].[AllowanceType] (
    [AllowanceTypeId] UNIQUEIDENTIFIER NOT NULL,
    [AllowanceTypeName] NVARCHAR(1000) NOT NULL,
    [AllowanceTypeCreatedAt] DATETIME NOT NULL CONSTRAINT [AllowanceType_AllowanceTypeCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [AllowanceTypeModifiedAt] DATETIME NOT NULL,
    [AllowanceTypeCreatedBy] NVARCHAR(1000) NOT NULL,
    [AllowanceTypeModifiedBy] NVARCHAR(1000),
    CONSTRAINT [AllowanceType_pkey] PRIMARY KEY CLUSTERED ([AllowanceTypeId])
);

-- CreateTable
CREATE TABLE [dbo].[ScholarshipStatus] (
    [ScholarshipStatusId] UNIQUEIDENTIFIER NOT NULL,
    [ScholarshipStatusName] NVARCHAR(1000) NOT NULL,
    [ScholarshipStatusCreatedAt] DATETIME NOT NULL CONSTRAINT [ScholarshipStatus_ScholarshipStatusCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [ScholarshipStatusModifiedAt] DATETIME NOT NULL,
    [ScholarshipStatusCreatedBy] NVARCHAR(1000) NOT NULL,
    [ScholarshipStatusModifiedBy] NVARCHAR(1000),
    CONSTRAINT [ScholarshipStatus_pkey] PRIMARY KEY CLUSTERED ([ScholarshipStatusId])
);

-- CreateTable
CREATE TABLE [dbo].[TSLSScheme] (
    [TSLSSchemeId] UNIQUEIDENTIFIER NOT NULL,
    [TSLSSchemeName] NVARCHAR(1000) NOT NULL,
    [TSLSSchemeCreatedAt] DATETIME NOT NULL CONSTRAINT [TSLSScheme_TSLSSchemeCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [TSLSSchemeModifiedAt] DATETIME NOT NULL,
    [TSLSSchemeCreatedBy] NVARCHAR(1000) NOT NULL,
    [TSLSSchemeModifiedBy] NVARCHAR(1000),
    CONSTRAINT [TSLSScheme_pkey] PRIMARY KEY CLUSTERED ([TSLSSchemeId])
);

-- CreateTable
CREATE TABLE [dbo].[Campus] (
    [CampusId] UNIQUEIDENTIFIER NOT NULL,
    [CampusName] NVARCHAR(1000) NOT NULL,
    [CampusCreatedAt] DATETIME NOT NULL CONSTRAINT [Campus_CampusCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [CampusModifiedAt] DATETIME NOT NULL,
    [CampusCreatedBy] NVARCHAR(1000) NOT NULL,
    [CampusModifiedBy] NVARCHAR(1000),
    CONSTRAINT [Campus_pkey] PRIMARY KEY CLUSTERED ([CampusId])
);

-- CreateTable
CREATE TABLE [dbo].[UniversityToCampusMapping] (
    [UniversityToCampusMappingId] UNIQUEIDENTIFIER NOT NULL,
    [UniversityToCampusMappingCreatedAt] DATETIME NOT NULL CONSTRAINT [UniversityToCampusMapping_UniversityToCampusMappingCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [UniversityToCampusMappingModifiedAt] DATETIME NOT NULL,
    [UniversityToCampusMappingCreatedBy] NVARCHAR(1000) NOT NULL,
    [UniversityToCampusMappingModifiedBy] NVARCHAR(1000),
    [CampusId] UNIQUEIDENTIFIER NOT NULL,
    [UniversityId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [UniversityToCampusMapping_pkey] PRIMARY KEY CLUSTERED ([UniversityToCampusMappingId])
);

-- CreateTable
CREATE TABLE [dbo].[SchemeName] (
    [SchemeNameId] UNIQUEIDENTIFIER NOT NULL,
    [SchemeName] NVARCHAR(1000) NOT NULL,
    [SchemeNameCreatedAt] DATETIME NOT NULL CONSTRAINT [SchemeName_SchemeNameCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [SchemeNameModifiedAt] DATETIME NOT NULL,
    [SchemeNameCreatedBy] NVARCHAR(1000) NOT NULL,
    [SchemeNameModifiedBy] NVARCHAR(1000),
    CONSTRAINT [SchemeName_pkey] PRIMARY KEY CLUSTERED ([SchemeNameId])
);

-- CreateTable
CREATE TABLE [dbo].[SubSchemeName] (
    [SubSchemeNameId] UNIQUEIDENTIFIER NOT NULL,
    [SubSchemeNameName] NVARCHAR(1000) NOT NULL,
    [SubSchemeNameCreatedAt] DATETIME NOT NULL CONSTRAINT [SubSchemeName_SubSchemeNameCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [SubSchemeNameModifiedAt] DATETIME NOT NULL,
    [SubSchemeNameCreatedBy] NVARCHAR(1000) NOT NULL,
    [SubSchemeNameModifiedBy] NVARCHAR(1000),
    CONSTRAINT [SubSchemeName_pkey] PRIMARY KEY CLUSTERED ([SubSchemeNameId])
);

-- CreateTable
CREATE TABLE [dbo].[SubSchemeNew] (
    [SubSchemeNewId] UNIQUEIDENTIFIER NOT NULL,
    [SubSchemeNewName] NVARCHAR(1000) NOT NULL,
    [SubSchemeNewCreatedAt] DATETIME NOT NULL CONSTRAINT [SubSchemeNew_SubSchemeNewCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [SubSchemeNewModifiedAt] DATETIME NOT NULL,
    [SubSchemeNewCreatedBy] NVARCHAR(1000) NOT NULL,
    [SubSchemeNewModifiedBy] NVARCHAR(1000),
    CONSTRAINT [SubSchemeNew_pkey] PRIMARY KEY CLUSTERED ([SubSchemeNewId])
);

-- CreateTable
CREATE TABLE [dbo].[Programme] (
    [ProgrammeId] UNIQUEIDENTIFIER NOT NULL,
    [ProgrammeName] NVARCHAR(1000) NOT NULL,
    [ProgrammeCreatedAt] DATETIME NOT NULL CONSTRAINT [Programme_ProgrammeCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [ProgrammeModifiedAt] DATETIME NOT NULL,
    [ProgrammeCreatedBy] NVARCHAR(1000) NOT NULL,
    [ProgrammeModifiedBy] NVARCHAR(1000),
    [MajorA] NVARCHAR(1000),
    [MajorB] NVARCHAR(1000),
    [Minor] NVARCHAR(1000),
    [Discipline] NVARCHAR(1000) NOT NULL,
    [DurationOfAwards] NVARCHAR(1000) NOT NULL,
    [SchemeNameId] UNIQUEIDENTIFIER NOT NULL,
    [SubSchemeNameId] UNIQUEIDENTIFIER NOT NULL,
    [SubSchemeNewId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [Programme_pkey] PRIMARY KEY CLUSTERED ([ProgrammeId])
);

-- CreateTable
CREATE TABLE [dbo].[MyCash] (
    [MyCashId] UNIQUEIDENTIFIER NOT NULL,
    [MyCashName] NVARCHAR(1000) NOT NULL,
    [DateTimeCreated] DATETIME NOT NULL CONSTRAINT [MyCash_DateTimeCreated_df] DEFAULT CURRENT_TIMESTAMP,
    [DateTimeModified] DATETIME NOT NULL,
    [UserCreatedBy] NVARCHAR(1000) NOT NULL,
    [UserModifiedBy] NVARCHAR(1000),
    CONSTRAINT [MyCash_pkey] PRIMARY KEY CLUSTERED ([MyCashId])
);

-- CreateTable
CREATE TABLE [dbo].[User] (
    [UserId] UNIQUEIDENTIFIER NOT NULL,
    [TIN] NVARCHAR(1000) NOT NULL,
    [FirstName] NVARCHAR(1000) NOT NULL,
    [LastName] NVARCHAR(1000) NOT NULL,
    [OtherName] NVARCHAR(1000),
    [Email] NVARCHAR(1000) NOT NULL,
    [HashedPassword] NVARCHAR(1000) NOT NULL,
    [UserProfilePicPath] NVARCHAR(1000),
    [IsAccountLoggedOut] BIT NOT NULL,
    [FailedLoginAttempts] INT,
    [IsUserLoggedIn] BIT NOT NULL,
    [UserCreatedAt] DATETIME NOT NULL CONSTRAINT [User_UserCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [UserModifiedAt] DATETIME NOT NULL,
    [EmailVerificationToken] NVARCHAR(1000),
    [EmailVerificationTokenExpiry] DATETIME2,
    [PasswordResetToken] NVARCHAR(1000),
    [PasswordResetTokenExpiry] DATETIME2,
    [LastLogin] DATETIME2,
    [LastPasswordResetDate] DATETIME2,
    [PhoneNumber] INT,
    [DepartmentToRoleMappingId] UNIQUEIDENTIFIER NOT NULL,
    [UserTypeId] UNIQUEIDENTIFIER NOT NULL,
    [AccountStatusId] UNIQUEIDENTIFIER NOT NULL,
    [TitleId] UNIQUEIDENTIFIER NOT NULL,
    [GenderId] UNIQUEIDENTIFIER NOT NULL,
    [EthnicityId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [User_pkey] PRIMARY KEY CLUSTERED ([UserId])
);

-- CreateTable
CREATE TABLE [dbo].[Staff] (
    [StaffId] UNIQUEIDENTIFIER NOT NULL,
    [StaffCreatedAt] DATETIME NOT NULL CONSTRAINT [Staff_StaffCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [StaffModifiedAt] DATETIME NOT NULL,
    [StaffCreatedBy] NVARCHAR(1000) NOT NULL,
    [StaffModifiedBy] NVARCHAR(1000),
    [userId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [Staff_pkey] PRIMARY KEY CLUSTERED ([StaffId])
);

-- CreateTable
CREATE TABLE [dbo].[Student] (
    [StudentId] UNIQUEIDENTIFIER NOT NULL,
    [StudentCreatedAt] DATETIME NOT NULL CONSTRAINT [Student_StudentCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [StudentModifiedAt] DATETIME NOT NULL,
    [StudentCreatedBy] NVARCHAR(1000) NOT NULL,
    [StudentModifiedBy] NVARCHAR(1000),
    [StudentIDNumber] NVARCHAR(1000) NOT NULL,
    [DateOfBirth] DATETIME,
    [ResidentialAddress] NVARCHAR(1000) NOT NULL,
    [PostalAddress] NVARCHAR(1000),
    [DateOfOffer] DATETIME NOT NULL,
    [CommencementTerm] NVARCHAR(1000) NOT NULL,
    [CommencementYear] NVARCHAR(1000) NOT NULL,
    [EndTerm] NVARCHAR(1000) NOT NULL,
    [EndYear] NVARCHAR(1000) NOT NULL,
    [Rent] NVARCHAR(1000),
    [Food] NVARCHAR(1000),
    [BusFare] NVARCHAR(1000),
    [IncidentalAllowance] NVARCHAR(1000),
    [BirthRegistrationNumber] INT NOT NULL,
    [PassportNumber] NVARCHAR(1000),
    [Hostel] NVARCHAR(1000),
    [BankAccountNumber] NVARCHAR(1000),
    [ETicketCardNumber] NVARCHAR(1000),
    [ApplicationId] NVARCHAR(1000) NOT NULL,
    [Remarks] NVARCHAR(1000) NOT NULL,
    [UniversityToCampusMappingId] UNIQUEIDENTIFIER,
    [BankNameId] UNIQUEIDENTIFIER NOT NULL,
    [ProgrammeId] UNIQUEIDENTIFIER NOT NULL,
    [MyCashId] UNIQUEIDENTIFIER NOT NULL,
    [NetworkTypeId] UNIQUEIDENTIFIER NOT NULL,
    [ProvinceId] UNIQUEIDENTIFIER NOT NULL,
    [ScholarshipStatusId] UNIQUEIDENTIFIER NOT NULL,
    [TSLSSchemeId] UNIQUEIDENTIFIER NOT NULL,
    [AllowanceTypeId] UNIQUEIDENTIFIER NOT NULL,
    [userId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [Student_pkey] PRIMARY KEY CLUSTERED ([StudentId])
);

-- CreateTable
CREATE TABLE [dbo].[Guarantor] (
    [GuarantorId] UNIQUEIDENTIFIER NOT NULL,
    [GuarantorCreatedAt] DATETIME NOT NULL CONSTRAINT [Guarantor_GuarantorCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [GuarantorModifiedAt] DATETIME NOT NULL,
    [GuarantorCreatedBy] NVARCHAR(1000) NOT NULL,
    [GuarantorModifiedBy] NVARCHAR(1000),
    [DateOfBirth] DATETIME NOT NULL,
    [ResidentialAddress] NVARCHAR(1000) NOT NULL,
    [PostalAddress] NVARCHAR(1000),
    [BirthRegistrationNumber] INT NOT NULL,
    [PassportNumber] NVARCHAR(1000),
    [ProvinceId] UNIQUEIDENTIFIER NOT NULL,
    [userId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [Guarantor_pkey] PRIMARY KEY CLUSTERED ([GuarantorId])
);

-- CreateTable
CREATE TABLE [dbo].[Organization] (
    [OrganizationId] UNIQUEIDENTIFIER NOT NULL,
    [OrganizationName] NVARCHAR(1000) NOT NULL,
    [OrganizationBranch] NVARCHAR(1000) NOT NULL,
    [OrganizationRegistrationNumber] NVARCHAR(1000) NOT NULL,
    [OrganizationRepresentativeFirstName] NVARCHAR(1000) NOT NULL,
    [OrganizationRepresentativeLastName] NVARCHAR(1000) NOT NULL,
    [OrganizationRepresentativeOtherName] NVARCHAR(1000),
    [Position] NVARCHAR(1000) NOT NULL,
    [OrganizationCreatedAt] DATETIME NOT NULL CONSTRAINT [Organization_OrganizationCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [OrganizationModifiedAt] DATETIME NOT NULL,
    [OrganizationCreatedBy] NVARCHAR(1000) NOT NULL,
    [OrganizationModifiedBy] NVARCHAR(1000),
    [userId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [Organization_pkey] PRIMARY KEY CLUSTERED ([OrganizationId])
);

-- CreateTable
CREATE TABLE [dbo].[University] (
    [UniversityId] UNIQUEIDENTIFIER NOT NULL,
    [UniversityName] NVARCHAR(1000) NOT NULL,
    [PrimaryEmail] NVARCHAR(1000) NOT NULL,
    [CCEmail] NVARCHAR(1000),
    [BCCEmail] NVARCHAR(1000),
    [UniversityRepresentativeFirstName] NVARCHAR(1000) NOT NULL,
    [UniversityRepresentativeLastName] NVARCHAR(1000) NOT NULL,
    [UniversityRepresentativeOtherName] NVARCHAR(1000),
    [UniversityCreatedAt] DATETIME NOT NULL CONSTRAINT [University_UniversityCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [UniversityModifiedAt] DATETIME NOT NULL,
    [UniversityCreatedBy] NVARCHAR(1000) NOT NULL,
    [UniversityModifiedBy] NVARCHAR(1000),
    [userId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [University_pkey] PRIMARY KEY CLUSTERED ([UniversityId])
);

-- AddForeignKey
ALTER TABLE [dbo].[DepartmentToRoleMapping] ADD CONSTRAINT [DepartmentToRoleMapping_DepartmentId_fkey] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Department]([DepartmentId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[DepartmentToRoleMapping] ADD CONSTRAINT [DepartmentToRoleMapping_RoleId_fkey] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[UserRole]([UserRoleId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[UniversityToCampusMapping] ADD CONSTRAINT [UniversityToCampusMapping_CampusId_fkey] FOREIGN KEY ([CampusId]) REFERENCES [dbo].[Campus]([CampusId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[UniversityToCampusMapping] ADD CONSTRAINT [UniversityToCampusMapping_UniversityId_fkey] FOREIGN KEY ([UniversityId]) REFERENCES [dbo].[University]([UniversityId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Programme] ADD CONSTRAINT [Programme_SchemeNameId_fkey] FOREIGN KEY ([SchemeNameId]) REFERENCES [dbo].[SchemeName]([SchemeNameId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Programme] ADD CONSTRAINT [Programme_SubSchemeNameId_fkey] FOREIGN KEY ([SubSchemeNameId]) REFERENCES [dbo].[SubSchemeName]([SubSchemeNameId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Programme] ADD CONSTRAINT [Programme_SubSchemeNewId_fkey] FOREIGN KEY ([SubSchemeNewId]) REFERENCES [dbo].[SubSchemeNew]([SubSchemeNewId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[User] ADD CONSTRAINT [User_DepartmentToRoleMappingId_fkey] FOREIGN KEY ([DepartmentToRoleMappingId]) REFERENCES [dbo].[DepartmentToRoleMapping]([DepartmentToRoleMappingId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[User] ADD CONSTRAINT [User_UserTypeId_fkey] FOREIGN KEY ([UserTypeId]) REFERENCES [dbo].[UserType]([UserTypeId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[User] ADD CONSTRAINT [User_AccountStatusId_fkey] FOREIGN KEY ([AccountStatusId]) REFERENCES [dbo].[AccountStatus]([AccountStatusId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[User] ADD CONSTRAINT [User_TitleId_fkey] FOREIGN KEY ([TitleId]) REFERENCES [dbo].[Title]([TitleId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[User] ADD CONSTRAINT [User_GenderId_fkey] FOREIGN KEY ([GenderId]) REFERENCES [dbo].[Gender]([GenderId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[User] ADD CONSTRAINT [User_EthnicityId_fkey] FOREIGN KEY ([EthnicityId]) REFERENCES [dbo].[Ethnicity]([EthnicityId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Staff] ADD CONSTRAINT [Staff_userId_fkey] FOREIGN KEY ([userId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_UniversityToCampusMappingId_fkey] FOREIGN KEY ([UniversityToCampusMappingId]) REFERENCES [dbo].[UniversityToCampusMapping]([UniversityToCampusMappingId]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_BankNameId_fkey] FOREIGN KEY ([BankNameId]) REFERENCES [dbo].[BankName]([BankNameId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_ProgrammeId_fkey] FOREIGN KEY ([ProgrammeId]) REFERENCES [dbo].[Programme]([ProgrammeId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_MyCashId_fkey] FOREIGN KEY ([MyCashId]) REFERENCES [dbo].[MyCash]([MyCashId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_NetworkTypeId_fkey] FOREIGN KEY ([NetworkTypeId]) REFERENCES [dbo].[NetworkType]([NetworkTypeId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_ProvinceId_fkey] FOREIGN KEY ([ProvinceId]) REFERENCES [dbo].[Province]([ProvinceId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_ScholarshipStatusId_fkey] FOREIGN KEY ([ScholarshipStatusId]) REFERENCES [dbo].[ScholarshipStatus]([ScholarshipStatusId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_TSLSSchemeId_fkey] FOREIGN KEY ([TSLSSchemeId]) REFERENCES [dbo].[TSLSScheme]([TSLSSchemeId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_AllowanceTypeId_fkey] FOREIGN KEY ([AllowanceTypeId]) REFERENCES [dbo].[AllowanceType]([AllowanceTypeId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Student] ADD CONSTRAINT [Student_userId_fkey] FOREIGN KEY ([userId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Guarantor] ADD CONSTRAINT [Guarantor_ProvinceId_fkey] FOREIGN KEY ([ProvinceId]) REFERENCES [dbo].[Province]([ProvinceId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Guarantor] ADD CONSTRAINT [Guarantor_userId_fkey] FOREIGN KEY ([userId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[Organization] ADD CONSTRAINT [Organization_userId_fkey] FOREIGN KEY ([userId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[University] ADD CONSTRAINT [University_userId_fkey] FOREIGN KEY ([userId]) REFERENCES [dbo].[User]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
